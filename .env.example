# Fishivo Environment Variables Template
# Copy this file to .env and fill in your actual values

# Application Settings
NODE_ENV=development
PORT=3000
API_URL=http://localhost:3001
WEB_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_APP_NAME=Fishivo
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
SUPABASE_JWT_SECRET=your_supabase_jwt_secret_here

# Authentication Secrets
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Facebook OAuth
FACEBOOK_APP_ID=your_facebook_app_id
NEXT_PUBLIC_FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_CALLBACK_URL=http://localhost:3001/auth/facebook/callback

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=http://localhost:3001/auth/google/callback
GOOGLE_REDIRECT_URI=http://localhost:3000/auth/callback
GOOGLE_WEB_CLIENT_ID=your_google_web_client_id
GOOGLE_IOS_CLIENT_ID=your_google_ios_client_id

# Cloudflare R2 Storage
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id
CLOUDFLARE_R2_ACCESS_KEY_ID=your_r2_access_key_id
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_r2_secret_access_key
CLOUDFLARE_R2_BUCKET_NAME=your_bucket_name
CLOUDFLARE_R2_PUBLIC_URL=your_r2_public_url
CLOUDFLARE_R2_ENDPOINT=your_r2_endpoint
CLOUDFLARE_R2_PUBLIC_DOMAIN=

# Mapbox Configuration
NEXT_PUBLIC_MAPBOX_TOKEN=your_mapbox_public_token
MAPBOX_DOWNLOADS_TOKEN=your_mapbox_downloads_token

# API Configuration
MAX_FILE_SIZE=5242880
API_TIMEOUT=10000
API_RETRIES=3
