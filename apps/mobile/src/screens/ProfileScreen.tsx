import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Alert,
  Image,
} from 'react-native';
import { apiService } from '../services/api';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from "../components/Icon";
import EquipmentCard from '../components/EquipmentCard';
import EquipmentSection from '../components/EquipmentSection';
import AddButton from '../../../../packages/ui/src/components/AddButton';
import Button from '../components/Button';
import TabSelector from '../../../../packages/ui/src/components/TabSelector';
import PostsGrid from '../../../../packages/ui/src/components/PostsGrid';
import LocationCard from '../../../../packages/ui/src/components/LocationCard';
import ProBadge from '../../../../packages/ui/src/components/ProBadge';
import DefaultAvatar from '../components/DefaultAvatar';
import UserDisplayName from '../../../../packages/ui/src/components/UserDisplayName';
import ProfileHeader from '../../../../packages/ui/src/components/ProfileHeader';
import UserProfileLayout from '../../../../packages/ui/src/components/UserProfileLayout';
import AppHeader from '../components/AppHeader';
import { theme } from '../theme';
import { ScreenContainer } from '../../../../packages/ui/src/components';
import { useAuth } from '../contexts/AuthContext';
import ConfirmModal from '../components/ConfirmModal';
import { useUnits } from '../hooks/useUnits';
import CatchCard from '../components/CatchCard';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { StackScreenProps } from '@react-navigation/stack';
import { RootStackParamList } from '../types/navigation';
import { MainTabParamList } from '../navigation/TabNavigator';

interface UserStats {
  totalCatches: number;
  totalSpots: number;
  followers: number;
  following: number;
}

interface FishingGear {
  id: string;
  name: string;
  category: string;
  brand?: string;
  icon: string;
  imageUrl?: string;
  condition: 'excellent' | 'good' | 'fair';
}

interface RecentCatch {
  id: string;
  species: string;
  weight: {
    value: number;
    unit: string;
    originalUnit: string;
    displayValue: string;
  };
  length?: {
    value: number;
    unit: string;
    originalUnit: string;
    displayValue: string;
  };
  date: string;
  location: string;
  photo?: string;
  imageUrl?: string;
  equipmentDetails?: Array<{
    id: string;
    name: string;
    category: string;
    brand?: string;
    icon: string;
    condition: 'excellent' | 'good' | 'fair';
  }>;
}

type ProfileScreenProps = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, 'Profile'>,
  StackScreenProps<RootStackParamList>
>;

const ProfileScreen: React.FC<ProfileScreenProps> = ({ navigation }) => {
  const { user } = useAuth();
  const { convertAndFormat } = useUnits();
  const [activeTab, setActiveTab] = useState<'catches' | 'gear' | 'spots'>('catches');
  const [showDeleteGearConfirm, setShowDeleteGearConfirm] = useState(false);
  const [gearToDelete, setGearToDelete] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [recentCatches, setRecentCatches] = useState<RecentCatch[]>([]);
  const [gearItems, setGearItems] = useState<FishingGear[]>([]);
  const [userLocations, setUserLocations] = useState<any[]>([]);

  // Kullanıcı profil bilgileri
  const [userProfile, setUserProfile] = useState({
    name: user?.name || user?.full_name || 'Kullanıcı',
    location: user?.location || 'Konum belirtilmemiş',
    bio: user?.bio || '',
    avatar: user?.avatar_url || user?.picture || '',
    isPro: user?.isPro || false,
    proSince: '2024'
  });

  // User statistics state
  const [userStats, setUserStats] = useState<UserStats>({
    totalCatches: 0,
    totalSpots: 0,
    followers: 0,
    following: 0
  });

  // AuthContext'teki user değişikliklerini dinle
  useEffect(() => {
    if (user) {
      setUserProfile({
        name: user.full_name || user.name || 'Kullanıcı',
        location: user.location || 'Konum belirtilmemiş',
        bio: user.bio || '',
        avatar: user.avatar_url || user.picture || '',
        isPro: user.isPro || false,
        proSince: '2024'
      });
    }
  }, [user]);

  // Helper function to get icon for gear category
  const getIconForGearCategory = (category: string): string => {
    switch (category?.toLowerCase()) {
      case 'rod':
        return 'fishing';
      case 'reel':
        return 'refresh-cw';
      case 'lure':
        return 'anchor';
      case 'line':
        return 'git-branch';
      case 'hook':
        return 'corner-down-right';
      case 'accessory':
        return 'tool';
      default:
        return 'package';
    }
  };

  const loadUserData = async () => {
    try {
      setLoading(true);
      
      // Kullanıcı ID'sini AuthContext'ten al
      const userId = user?.id;
      if (!userId) {
        console.warn('⚠️ No user ID available');
        setLoading(false);
        return;
      }
      
      console.log('🔍 ProfileScreen: Loading data for user', userId);
      
      // Paralel olarak tüm verileri çek
      const [userProfileData, userStatsData, userPosts, userLocations, userGear] = await Promise.all([
        // Kullanıcı profil bilgilerini çek
        apiService.getUserProfile(userId).catch(err => {
          console.error('Profil yüklenirken hata:', err);
          return null;
        }),
        
        // Kullanıcı istatistiklerini çek
        apiService.getUserStats().catch(err => {
          console.warn('Stats data failed:', err);
          return { totalCatches: 0, totalSpots: 0, followers: 0, following: 0 };
        }),
        
        // Kullanıcının postlarını çek
        apiService.getPosts(1, 10, { userId }).catch(err => {
          console.warn('Posts data failed:', err);
          return { items: [] };
        }),
        
        // Kullanıcının lokasyonlarını çek
        apiService.getUserLocations().catch(err => {
          console.warn('Locations data failed:', err);
          return [];
        }),
        
        // Kullanıcı ekipmanlarını çek
        apiService.getUserEquipment().catch(err => {
          console.warn('Equipment data failed:', err);
          return [];
        })
      ]);

      // İstatistikleri güncelle (totalSpots'u da userStats'dan kullan)
      setUserStats({
        ...userStatsData,
        totalSpots: userStatsData.totalSpots || userLocations.length // Fallback olarak lokasyon sayısını kullan
      });

      console.log(`✅ ProfileScreen: User profile updated:`, {
        name: userProfile.name,
        bio: userProfile.bio,
        location: userProfile.location,
        stats: userStatsData
      });
      
      // API verisini mevcut format'a çevir
      const formattedCatches = userPosts.items.map((post: any) => ({
        id: post.id.toString(),
        species: post.catch_details?.species_name || 'Balık',
        weight: {
          value: post.catch_details?.weight || 0,
          unit: 'kg',
          originalUnit: 'kg',
          displayValue: `${post.catch_details?.weight || 0} kg`
        },
        date: post.created_at,
        location: post.location?.address || 'Bilinmeyen Konum',
        photo: post.image_url || undefined
      }));
      
      // Ekipmanları formatlayarak state'e kaydet
      setGearItems(userGear.map((gear: any) => ({
        id: gear.id,
        name: gear.name,
        category: gear.category,
        brand: gear.brand,
        icon: getIconForGearCategory(gear.category),
        condition: gear.condition || 'good'
      })));
      
      // Lokasyonları formatlayarak state'e kaydet
      const formattedLocations = userLocations.map((spot: any) => ({
        id: spot.id.toString(),
        name: spot.name,
        location: spot.location?.address || spot.address || 'Bilinmeyen Konum',
        coordinates: [
          spot.location?.longitude || spot.longitude || 0,
          spot.location?.latitude || spot.latitude || 0
        ],
        type: spot.is_private ? 'private-spot' : 'spot',
        catches: spot.catch_count || 0,
        isFavorite: spot.is_favorite || false
      }));
      
      console.log(`🗺️ ProfileScreen: Formatted ${formattedLocations.length} locations`);
      setUserLocations(formattedLocations);
      
      // Yakalanan balıkları state'e kaydet
      setRecentCatches(formattedCatches.slice(0, 3));
      
    } catch (error) {
      console.error('Failed to load user data:', error);
      Alert.alert('Hata', 'Kullanıcı verileri yüklenirken bir sorun oluştu.');
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUserData();
  }, [user]);

  const tabs = [
    { id: 'catches', label: 'Avlar', icon: 'fish' },
    { id: 'gear', label: 'Ekipmanlar', icon: 'backpack' },
    { id: 'spots', label: 'Spotlar', icon: 'anchor' },
  ];

  const handleSelectSpot = (location: any) => {
    navigation.navigate('Map', { selectedLocation: location });
  };

  const handleDeleteGear = (gearId: string) => {
    setGearToDelete(gearId);
    setShowDeleteGearConfirm(true);
  };

  const confirmDeleteGear = () => {
    if (gearToDelete) {
      setGearItems(prev => prev.filter(item => item.id !== gearToDelete));
      setGearToDelete(null);
    }
    setShowDeleteGearConfirm(false);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'catches':
        return (
          <View style={styles.tabContent}>
            {recentCatches.length === 0 ? (
              <View style={styles.emptyState}>
                <Icon name="fishing" size={48} color={theme.colors.primary} />
                <Text style={styles.emptyStateText}>Henüz bir yakalama paylaşmadınız</Text>
                <Text style={styles.emptyStateSubtext}>İlk yakalamayı paylaşmak için "+" butonuna tıklayın</Text>
              </View>
            ) : (
              <FlatList
                data={recentCatches}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <TouchableOpacity 
                    style={styles.catchCard}
                    onPress={() => navigation.navigate('FishDetail', { catchId: item.id })}
                  >
                    <View style={styles.catchPhoto}>
                      {item.photo ? (
                        <Image source={{ uri: item.photo }} style={styles.catchPhotoImage} />
                      ) : (
                        <Icon name="fish" size={24} color={theme.colors.primary} />
                      )}
                    </View>
                    <View style={styles.catchInfo}>
                      <Text style={styles.catchSpecies}>{item.species}</Text>
                      <Text style={styles.catchWeight}>{item.weight.displayValue}</Text>
                      <Text style={styles.catchLocation}>{item.location}</Text>
                      <Text style={styles.catchDate}>
                        {item.date ? new Date(item.date).toLocaleDateString('tr-TR') : ''}
                      </Text>
                    </View>
                  </TouchableOpacity>
                )}
                contentContainerStyle={{ gap: theme.spacing.md }}
                showsVerticalScrollIndicator={false}
              />
            )}
          </View>
        );
      case 'spots':
        return (
          <View style={styles.tabContent}>
            {userLocations.length === 0 ? (
              <View style={styles.emptyState}>
                <Icon name="map-pin" size={48} color={theme.colors.primary} />
                <Text style={styles.emptyStateText}>Henüz bir balık noktası kaydetmediniz</Text>
                <Text style={styles.emptyStateSubtext}>İlk noktayı eklemek için haritayı kullanın</Text>
              </View>
            ) : (
              <>
                {/* Spot Statistics */}
                <View style={styles.spotStats}>
                  <View style={styles.statItem}>
                    <Icon name="map-pin" size={24} color={theme.colors.primary} />
                    <Text style={styles.spotStatNumber}>{userLocations.length}</Text>
                    <Text style={styles.spotStatLabel}>Toplam Nokta</Text>
                  </View>
                  <View style={styles.statItem}>
                    <Icon name="star" size={24} color={theme.colors.warning} />
                    <Text style={styles.spotStatNumber}>
                      {userLocations.filter(spot => spot.isFavorite).length}
                    </Text>
                    <Text style={styles.spotStatLabel}>Favori</Text>
                  </View>
                  <View style={styles.statItem}>
                    <Icon name="fish" size={24} color={theme.colors.success} />
                    <Text style={styles.spotStatNumber}>
                      {userLocations.reduce((total, spot) => total + (spot.catches || 0), 0)}
                    </Text>
                    <Text style={styles.spotStatLabel}>Toplam Av</Text>
                  </View>
                </View>

                {/* Recent Spots */}
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>Son Kullanılan Noktalar</Text>
                </View>
                <View style={styles.recentSpots}>
                  {userLocations.slice(0, 3).map(spot => (
                    <TouchableOpacity 
                      key={spot.id} 
                      style={styles.recentSpotItem}
                      onPress={() => handleSelectSpot(spot)}
                    >
                      <View style={styles.spotIcon}>
                        <Icon name="map-pin" size={20} color={theme.colors.primary} />
                      </View>
                      <View style={styles.spotInfo}>
                        <Text style={styles.spotName}>{spot.name}</Text>
                        <Text style={styles.spotLocation}>{spot.location}</Text>
                      </View>
                      <TouchableOpacity style={styles.spotAction} onPress={() => handleSelectSpot(spot)}>
                        <Icon name="chevron-right" size={20} color={theme.colors.textSecondary} />
                      </TouchableOpacity>
                    </TouchableOpacity>
                  ))}
                </View>

                {userLocations.length > 3 && (
                  <TouchableOpacity 
                    style={styles.viewAllButton}
                    onPress={() => navigation.navigate('LocationManagement')}
                  >
                    <Text style={styles.viewAllText}>Tüm Noktaları Görüntüle</Text>
                    <Icon name="arrow-right" size={16} color={theme.colors.primary} />
                  </TouchableOpacity>
                )}
              </>
            )}
          </View>
        );
      case 'gear':
        return (
          <EquipmentSection
            equipment={gearItems}
            title="Ekipmanlar"
            showAddButton={true}
            onAddPress={() => navigation.navigate('AddGear')}
            onDeleteGear={handleDeleteGear}
          />
        );
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title="Profil"
        rightButtons={[
          {
            icon: "bell",
            onPress: () => navigation.navigate('Notifications')
          },
          {
            icon: "settings",
            onPress: () => navigation.navigate('Settings')
          }
        ]}
      />

      <ScreenContainer>
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Profile Section */}
          <UserProfileLayout
            userData={{
              name: userProfile.name,
              location: userProfile.location,
              bio: userProfile.bio,
              avatar: userProfile.avatar,
              catchCount: userStats.totalCatches,
              followers: userStats.followers,
              following: userStats.following,
              isPro: userProfile.isPro,
            }}
            isOwnProfile={true}
            onPrimaryAction={() => navigation.navigate('EditProfile')}
            onShareAction={() => {
          
            }}
            onProPress={() => navigation.navigate('Premium')}
            noPadding={true}
          />

          {/* Haritam Section */}
          <View style={styles.quickActionsSection}>
            <TouchableOpacity 
              style={styles.yourMapCard}
              onPress={() => navigation.navigate('YourMap')}
            >
              <View style={styles.yourMapIcon}>
                <Icon name="navigation" size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.yourMapContent}>
                <Text style={styles.yourMapTitle}>Haritam</Text>
                <Text style={styles.yourMapSubtitle}>
                  Favori spotlar, gizli noktalar ve av geçmişiniz
                </Text>
              </View>
              <Icon name="chevron-right" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* Tab Navigation */}
          <TabSelector
            tabs={tabs}
            activeTab={activeTab}
            onTabPress={(tabId) => setActiveTab(tabId as 'catches' | 'gear' | 'spots')}
          />

          {/* Tab Content */}
          {renderTabContent()}

          {/* Bottom Padding */}
          <View style={{ height: theme.spacing.xl }} />
        </ScrollView>
      </ScreenContainer>

      <ConfirmModal
        visible={showDeleteGearConfirm}
        title="Ekipmanı Sil"
        message="Bu ekipmanı silmek istediğinizden emin misiniz?"
        onConfirm={confirmDeleteGear}
        onCancel={() => {
          setShowDeleteGearConfirm(false);
          setGearToDelete(null);
        }}
        confirmText="Sil"
        cancelText="İptal"
        type="destructive"
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  userCard: {
    backgroundColor: theme.colors.surface,
    marginBottom: theme.spacing.md,
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.xl,
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: theme.spacing.md,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: theme.colors.surfaceVariant,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 4,
    borderColor: theme.colors.primary,
  },
  avatarText: {
    fontSize: 40,
  },
  onlineBadge: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
  },
  onlineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: theme.colors.success,
  },
  userName: {
    fontSize: theme.typography.xl,
    fontWeight: theme.typography.bold,
    color: theme.colors.text,
    textAlign: 'center',
  },

  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  userLocation: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
  userBio: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: theme.spacing.lg,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  proStatusBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing.xs,
    marginTop: theme.spacing.md,
    padding: theme.spacing.sm,
    backgroundColor: `${theme.colors.accent}10`,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: `${theme.colors.accent}20`,
  },
  proStatusText: {
    fontSize: theme.typography.sm,
    color: theme.colors.accent,
    fontWeight: theme.typography.medium,
  },

  statsContainer: {
    marginBottom: theme.spacing.md,
    gap: theme.spacing.md,
  },
  statsRow: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  statCard: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: theme.typography.xl,
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
    marginBottom: theme.spacing.xs,
  },
  statLabel: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.medium,
    textAlign: 'center',
  },

  tabContent: {
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    paddingHorizontal: theme.spacing.xs,
  },
  sectionTitle: {
    fontSize: theme.typography.lg,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
  },
  seeAll: {
    fontSize: theme.typography.sm,
    color: theme.colors.primary,
    fontWeight: theme.typography.medium,
  },

  catchCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    gap: theme.spacing.md,
  },
  catchPhoto: {
    width: 50,
    height: 50,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surfaceVariant,
    alignItems: 'center',
    justifyContent: 'center',
  },
  catchPhotoText: {
    fontSize: 20,
  },
  catchInfo: {
    flex: 1,
  },
  catchSpecies: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    marginBottom: 2,
  },
  catchWeight: {
    fontSize: theme.typography.sm,
    color: theme.colors.primary,
    fontWeight: theme.typography.medium,
    marginBottom: 2,
  },
  catchLocation: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
  },
  catchDate: {
    fontSize: theme.typography.xs,
    color: theme.colors.textTertiary,
  },

  emptyContainer: {
    padding: theme.spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  emptyText: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.medium,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: theme.spacing.xs,
  },
  emptyState: {
    padding: theme.spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  emptyStateText: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.medium,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
    marginTop: theme.spacing.md,
  },
  emptyStateSubtext: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: theme.spacing.xs,
  },

  // Spot Stats Styles
  spotStats: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    padding: theme.spacing.sm,
  },
  spotStatNumber: {
    fontSize: theme.typography.lg,
    fontWeight: theme.typography.bold,
    color: theme.colors.text,
    marginTop: theme.spacing.xs,
  },
  spotStatLabel: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  // Recent Spots Styles
  recentSpots: {
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.md,
  },
  recentSpotItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    gap: theme.spacing.md,
  },
  spotIcon: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surfaceVariant,
    alignItems: 'center',
    justifyContent: 'center',
  },
  spotInfo: {
    flex: 1,
  },
  spotName: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.medium,
    color: theme.colors.text,
    marginBottom: 2,
  },
  spotLocation: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
  spotAction: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    gap: theme.spacing.xs,
  },
  viewAllText: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.medium,
    color: theme.colors.primary,
  },
  favoriteSpotsSection: {
    marginBottom: theme.spacing.md,
  },
  sectionSubtitle: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.medium,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  spotsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },

  // YourMap Section Styles
  quickActionsSection: {
    marginBottom: theme.spacing.lg,
  },
  yourMapCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  yourMapIcon: {
    width: 48,
    height: 48,
    borderRadius: theme.borderRadius.md,
    backgroundColor: `${theme.colors.primary}10`,
    alignItems: 'center',
    justifyContent: 'center',
  },
  yourMapContent: {
    flex: 1,
  },
  yourMapTitle: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.semibold,
    color: theme.colors.text,
    marginBottom: 2,
  },
  yourMapSubtitle: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    lineHeight: 18,
  },

  // Catch card styles for the list view
  catchPhotoImage: {
    width: 50,
    height: 50,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surfaceVariant,
  },

});

export default ProfileScreen; 