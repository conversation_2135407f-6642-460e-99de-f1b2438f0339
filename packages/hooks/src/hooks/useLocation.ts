import { useState, useCallback, useEffect } from 'react';
import { apiService } from '@fishivo/services';

// Local type definitions to avoid export issues
interface Location {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  country?: string;
}



interface LocationState {
  currentLocation: Location | null;
  isLoading: boolean;
  error: string | null;
  isLocationEnabled: boolean;
  accuracy?: number;
}



export const useLocation = () => {
  const [locationState, setLocationState] = useState<LocationState>({
    currentLocation: null,
    isLoading: false,
    error: null,
    isLocationEnabled: false,
  });

  const [watchId, setWatchId] = useState<number | null>(null);

  useEffect(() => {
    checkLocationPermission();
    return () => {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
      }
    };
  }, [watchId]);

  const checkLocationPermission = useCallback(async () => {
    try {
      if (!navigator.geolocation) {
        setLocationState(prev => ({
          ...prev,
          isLocationEnabled: false,
          error: 'Konum servisi bu cihazda desteklenmiyor',
        }));
        return false;
      }

      // Check if location is available
      setLocationState(prev => ({
        ...prev,
        isLocationEnabled: true,
        error: null,
      }));

      return true;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Konum izni kontrol edilirken hata oluştu';
      setLocationState(prev => ({
        ...prev,
        isLocationEnabled: false,
        error: message,
      }));
      return false;
    }
  }, []);

  const getCurrentLocation = useCallback(async (): Promise<Location | null> => {
    try {
      setLocationState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('📍 Getting current location...');

      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 60000,
          }
        );
      });

      const location: Location = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
      };

      // Try to get address from coordinates
      try {
        const locationName = await apiService.getLocationName(
          position.coords.latitude,
          position.coords.longitude
        );
        
        if (locationName) {
          location.address = locationName.address;
          location.city = locationName.city;
          location.country = locationName.country;
        }
      } catch (addressError) {
        console.warn('Could not get address for location:', addressError);
      }

      setLocationState(prev => ({
        ...prev,
        currentLocation: location,
        isLoading: false,
        accuracy: position.coords.accuracy,
      }));

      console.log('✅ Current location obtained:', location);

      return location;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Konum alınırken hata oluştu';
      console.error('❌ Get current location error:', error);
      setLocationState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const watchLocation = useCallback(() => {
    if (!navigator.geolocation) {
      setLocationState(prev => ({
        ...prev,
        error: 'Konum servisi bu cihazda desteklenmiyor',
      }));
      return;
    }

    console.log('👀 Starting location watch...');

    const id = navigator.geolocation.watchPosition(
      (position) => {
        const location: Location = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        };

        setLocationState(prev => ({
          ...prev,
          currentLocation: location,
          accuracy: position.coords.accuracy,
          error: null,
        }));

        console.log('📍 Location updated:', location);
      },
      (error) => {
        const message = error.message || 'Konum takibi sırasında hata oluştu';
        console.error('❌ Location watch error:', error);
        setLocationState(prev => ({
          ...prev,
          error: message,
        }));
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 60000,
      }
    );

    setWatchId(id);
  }, []);

  const stopWatching = useCallback(() => {
    if (watchId !== null) {
      console.log('🛑 Stopping location watch...');
      navigator.geolocation.clearWatch(watchId);
      setWatchId(null);
    }
  }, [watchId]);

  const getUserLocations = useCallback(async () => {
    try {
      console.log('🗺️ Getting user saved locations...');

      // Use the real API method
      const locations = await apiService.getUserLocations();

      console.log('✅ User locations fetched:', locations);

      return locations;
    } catch (error) {
      console.error('❌ Get user locations error:', error);
      throw error;
    }
  }, []);

  const clearError = useCallback(() => {
    setLocationState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    currentLocation: locationState.currentLocation,
    isLoading: locationState.isLoading,
    error: locationState.error,
    isLocationEnabled: locationState.isLocationEnabled,
    accuracy: locationState.accuracy,
    getCurrentLocation,
    watchLocation,
    stopWatching,
    getUserLocations,
    checkLocationPermission,
    clearError,
  };
};
