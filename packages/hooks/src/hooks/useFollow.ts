import { useState, useCallback } from 'react';
import { apiService } from '@fishivo/services';

interface FollowState {
  isFollowing: boolean;
  isPending: boolean;
  error: string | null;
  followersCount: number;
  followingCount: number;
}

export const useFollow = (userId?: string) => {
  const [followState, setFollowState] = useState<FollowState>({
    isFollowing: false,
    isPending: false,
    error: null,
    followersCount: 0,
    followingCount: 0,
  });

  const checkFollowStatus = useCallback(async (targetUserId: string) => {
    try {
      console.log('🔍 Checking follow status for user:', targetUserId);

      // Use the real API method
      const isFollowing = await apiService.isFollowing(targetUserId);

      console.log('✅ Follow status checked:', { userId: targetUserId, isFollowing });

      setFollowState(prev => ({
        ...prev,
        isFollowing,
        error: null,
      }));

      return isFollowing;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Takip durumu kontrol edilirken hata o<PERSON>';
      console.error('❌ Check follow status error:', error);
      setFollowState(prev => ({
        ...prev,
        error: message,
      }));
      throw error;
    }
  }, []);

  const followUser = useCallback(async (targetUserId: string) => {
    if (followState.isPending) return;

    try {
      setFollowState(prev => ({
        ...prev,
        isPending: true,
        error: null
      }));

      console.log('👥 Following user:', targetUserId);

      // Use the real API method
      await apiService.followUser(targetUserId);

      console.log('✅ User followed successfully');

      setFollowState(prev => ({
        ...prev,
        isFollowing: true,
        followersCount: prev.followersCount + 1,
        isPending: false,
      }));

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Kullanıcı takip edilirken hata oluştu';
      console.error('❌ Follow user error:', error);
      setFollowState(prev => ({
        ...prev,
        isPending: false,
        error: message,
      }));
      throw error;
    }
  }, [followState.isPending]);

  const unfollowUser = useCallback(async (targetUserId: string) => {
    if (followState.isPending) return;

    try {
      setFollowState(prev => ({
        ...prev,
        isPending: true,
        error: null
      }));

      console.log('👥 Unfollowing user:', targetUserId);

      // Use the real API method
      await apiService.unfollowUser(targetUserId);

      console.log('✅ User unfollowed successfully');

      setFollowState(prev => ({
        ...prev,
        isFollowing: false,
        followersCount: Math.max(prev.followersCount - 1, 0),
        isPending: false,
      }));

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Kullanıcı takipten çıkarılırken hata oluştu';
      console.error('❌ Unfollow user error:', error);
      setFollowState(prev => ({
        ...prev,
        isPending: false,
        error: message,
      }));
      throw error;
    }
  }, [followState.isPending]);

  const toggleFollow = useCallback(async (targetUserId: string) => {
    if (followState.isFollowing) {
      return await unfollowUser(targetUserId);
    } else {
      return await followUser(targetUserId);
    }
  }, [followState.isFollowing, followUser, unfollowUser]);

  const getSocialCounts = useCallback(async (targetUserId: string) => {
    try {
      console.log('📊 Getting social counts for user:', targetUserId);

      // Use the real API method
      const counts = await apiService.getUserSocialCounts(targetUserId);

      console.log('✅ Social counts fetched:', counts);

      setFollowState(prev => ({
        ...prev,
        followersCount: counts.followers,
        followingCount: counts.following,
        error: null,
      }));

      return counts;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Sosyal sayılar alınırken hata oluştu';
      console.error('❌ Get social counts error:', error);
      setFollowState(prev => ({
        ...prev,
        error: message,
      }));
      throw error;
    }
  }, []);

  const clearError = useCallback(() => {
    setFollowState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    isFollowing: followState.isFollowing,
    isPending: followState.isPending,
    error: followState.error,
    followersCount: followState.followersCount,
    followingCount: followState.followingCount,
    checkFollowStatus,
    followUser,
    unfollowUser,
    toggleFollow,
    getSocialCounts,
    clearError,
  };
};
