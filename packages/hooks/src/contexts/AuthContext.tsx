import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiService, supabase } from '@fishivo/services';

interface User {
  id: string;
  email: string;
  fullName?: string;
  avatar?: string;
  username?: string;
  bio?: string;
  location?: string;
  [key: string]: any;
}

interface AuthContextValue {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  authError: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, fullName: string, username?: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  clearAuthError: () => void;
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [authError, setAuthError] = useState<string | null>(null);

  const isAuthenticated = !!user;

  const clearAuthError = () => {
    setAuthError(null);
  };

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      setAuthError(null);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw new Error(error.message);
      }

      if (data.user) {
        // Get user profile from your backend
        try {
          const response = await apiService.getCurrentUser();
          if (response.success && response.data) {
            setUser(response.data);
          }
        } catch (profileError) {
          // If profile fetch fails, create basic user object
          setUser({
            id: data.user.id,
            email: data.user.email || '',
            fullName: data.user.user_metadata?.full_name,
          });
        }

        // Store session token
        if (data.session?.access_token) {
          await AsyncStorage.setItem('auth_token', data.session.access_token);
        }
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed';
      setAuthError(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, fullName: string, username?: string) => {
    try {
      setIsLoading(true);
      setAuthError(null);
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            username: username,
          },
        },
      });

      if (error) {
        throw new Error(error.message);
      }

      if (data.user) {
        // Create user profile in your backend
        try {
          const profileData = {
            id: data.user.id,
            email: data.user.email || '',
            fullName,
            username,
          };
          
          // You might want to call your backend API to create user profile
          // await apiService.createUserProfile(profileData);
          
          setUser(profileData);
        } catch (profileError) {
          console.error('Profile creation error:', profileError);
          // Still set basic user data even if profile creation fails
          setUser({
            id: data.user.id,
            email: data.user.email || '',
            fullName,
            username,
          });
        }

        // Store session token if available
        if (data.session?.access_token) {
          await AsyncStorage.setItem('auth_token', data.session.access_token);
        }
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Registration failed';
      setAuthError(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithGoogle = async () => {
    try {
      setIsLoading(true);
      setAuthError(null);
      // Mobilde deep link/redirect ayarını kendi scheme'inize göre güncelleyin
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: 'yourapp://auth-callback', // Bunu kendi mobil deep link scheme'inize göre değiştirin
        },
      });
      if (error) {
        throw new Error(error.message);
      }
      if (data?.url) {
        // Eğer React Native'de iseniz:
        // import { Linking } from 'react-native';
        // await Linking.openURL(data.url);
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Google sign in failed';
      setAuthError(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await supabase.auth.signOut();
      await AsyncStorage.removeItem('auth_token');
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (data: Partial<User>) => {
    try {
      const updatedUser = await apiService.updateProfile(data);
      // updatedUser is already the user object, not wrapped in ApiResponse
      if (updatedUser && typeof updatedUser === 'object') {
        setUser(updatedUser);
      }
    } catch (error) {
      throw error;
    }
  };

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      
      // Check Supabase session first
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (session && session.user) {
        try {
          // Try to get user profile from backend
          const response = await apiService.getCurrentUser();
          if (response.success && response.data) {
            setUser(response.data);
          } else {
            // If backend profile doesn't exist, create basic user from Supabase
            setUser({
              id: session.user.id,
              email: session.user.email || '',
              fullName: session.user.user_metadata?.full_name,
              username: session.user.user_metadata?.username,
            });
          }
          
          // Store session token
          if (session.access_token) {
            await AsyncStorage.setItem('auth_token', session.access_token);
          }
        } catch (error) {
          console.error('Profile fetch error:', error);
          // Still set basic user data even if profile fetch fails
          setUser({
            id: session.user.id,
            email: session.user.email || '',
            fullName: session.user.user_metadata?.full_name,
            username: session.user.user_metadata?.username,
          });
        }
      } else {
        // No session, clear local storage
        await AsyncStorage.removeItem('auth_token');
        setUser(null);
      }
    } catch (error) {
      console.error('Auth check error:', error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkAuthStatus();
    
    // Listen to auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event: string, session: any) => {
        console.log('Auth state changed:', event, session?.user?.email);
        
        if (event === 'SIGNED_IN' && session?.user) {
          try {
            // Try to get user profile from backend
            const response = await apiService.getCurrentUser();
            if (response.success && response.data) {
              setUser(response.data);
            } else {
              // Create basic user from Supabase
              setUser({
                id: session.user.id,
                email: session.user.email || '',
                fullName: session.user.user_metadata?.full_name,
                username: session.user.user_metadata?.username,
              });
            }
            
            // Store session token
            if (session.access_token) {
              await AsyncStorage.setItem('auth_token', session.access_token);
            }
          } catch (error) {
            console.error('Profile fetch error:', error);
            // Still set basic user data
            setUser({
              id: session.user.id,
              email: session.user.email || '',
              fullName: session.user.user_metadata?.full_name,
              username: session.user.user_metadata?.username,
            });
          }
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          await AsyncStorage.removeItem('auth_token');
        }
      }
    );
    
    // Cleanup subscription on unmount
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const value: AuthContextValue = {
    user,
    isAuthenticated,
    isLoading,
    authError,
    login,
    register,
    signInWithGoogle,
    logout,
    updateProfile,
    clearAuthError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};