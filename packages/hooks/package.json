{"name": "@fishivo/hooks", "version": "1.0.0", "description": "Shared React hooks for Fishivo", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx"}, "keywords": ["hooks", "react", "react-native"], "author": "Fishivo Team", "license": "MIT", "dependencies": {"@fishivo/services": "workspace:*", "@fishivo/shared": "workspace:*", "@supabase/supabase-js": "^2.50.2", "axios": "^1.6.2"}, "devDependencies": {"@types/react": "^18.2.0", "eslint": "^8.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": ">=18.0.0"}}