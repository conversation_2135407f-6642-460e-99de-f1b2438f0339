import { NavigatorScreenParams } from '@react-navigation/native';

// MainTabParamList type definition (moved here to avoid circular dependency)
export type MainTabParamList = {
  Home: undefined;
  Map: undefined;
  AddCatch: undefined;
  Notifications: undefined;
  Profile: undefined;
};

export type RootTabParamList = {
  Home: undefined;
  Map: undefined;
  AddCatch: undefined;
  Notifications: undefined;
  Profile: undefined;
};

export type RootStackParamList = {
  Auth: undefined;
  MainTabs: NavigatorScreenParams<MainTabParamList>;
  UserProfile: { userId: string };
  Settings: undefined;
  EditProfile: undefined;
  NotificationSettings: undefined;
  UnitsSettings: undefined;
  BlockedUsers: undefined;
  AddGear: { gearId?: string } | undefined;
  AddCatch: undefined;
  AddSpot: undefined;
  PostDetail: { postId: string };
  LocationManagement: undefined;
  AddLocation: undefined;
  ExploreSearch: undefined;
  FishSpecies: undefined;
  FishDetail: { catchId: string };
  Notifications: undefined;
  YourMap: undefined;
  Premium: undefined;
};