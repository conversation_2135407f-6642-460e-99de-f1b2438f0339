{"name": "@fishivo/shared", "version": "1.0.0", "description": "Shared utilities and types for Fishivo", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "lint": "eslint src/**/*.ts", "test": "jest"}, "keywords": ["fishivo", "shared", "utilities", "types"], "author": "Fishivo Team", "license": "MIT", "devDependencies": {"@types/node": "^20.5.9", "eslint": "^8.49.0", "tsup": "^8.0.0", "typescript": "^5.2.2"}, "files": ["dist"]}