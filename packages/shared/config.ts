// Shared Configuration for Fishivo Project
// Used by Backend, Web, and Mobile applications

// Development IP configuration - centralized IP management
// For Android emulator use ********, for iOS use your local IP
const DEVELOPMENT_IP = '********'; // Changed from *********** to ******** for Android compatibility

// Environment detection
const isDevelopment = process.env.NODE_ENV === 'development';

// Base configuration
export const CONFIG = {
  // Environment
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // API URLs - using centralized IP for development
  API_URL: process.env.API_URL || process.env.NEXT_PUBLIC_API_URL || (isDevelopment ? `http://${DEVELOPMENT_IP}:4000` : 'https://api.fishivo.com'),
  WEB_URL: process.env.WEB_URL || process.env.NEXT_PUBLIC_APP_URL || (isDevelopment ? `http://${DEVELOPMENT_IP}:3010` : 'https://fishivo.com'),
  
  // Database
  DATABASE_URL: process.env.DATABASE_URL,
  
  // Supabase
  SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
  SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
  
  // OAuth
  FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID || process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
  FACEBOOK_APP_SECRET: process.env.FACEBOOK_APP_SECRET,
  
  // Security
  JWT_SECRET: process.env.JWT_SECRET,
  SESSION_SECRET: process.env.SESSION_SECRET,
  
  // File Upload
  CLOUDFLARE_ACCOUNT_ID: process.env.CLOUDFLARE_ACCOUNT_ID,
  CLOUDFLARE_R2_ACCESS_KEY_ID: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
  CLOUDFLARE_R2_SECRET_ACCESS_KEY: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
  CLOUDFLARE_R2_BUCKET_NAME: process.env.CLOUDFLARE_R2_BUCKET_NAME,
  CLOUDFLARE_R2_PUBLIC_URL: process.env.CLOUDFLARE_R2_PUBLIC_URL,
};

// API Configuration for different packages
export const API_CONFIG = {
  baseURL: CONFIG.API_URL,
  webURL: CONFIG.WEB_URL,
  timeout: 10000,
  
  // Endpoints
  endpoints: {
    auth: '/auth',
    users: '/api/users',
    posts: '/api/posts',
    catches: '/api/catches',
    species: '/api/species',
    spots: '/api/spots',
    upload: '/api/upload',
    weather: '/api/weather',
  },
  
  // OAuth URLs
  oauth: {
    facebook: `${CONFIG.API_URL}/auth/facebook`,
  },
};

// Helper functions
export const getApiUrl = (endpoint: string): string => {
  return `${CONFIG.API_URL}${endpoint}`;
};

export const getWebUrl = (path: string = ''): string => {
  return `${CONFIG.WEB_URL}${path}`;
};

// Environment helpers
export const isProduction = (): boolean => CONFIG.NODE_ENV === 'production';
export const isDev = (): boolean => CONFIG.NODE_ENV === 'development';
export const isTest = (): boolean => CONFIG.NODE_ENV === 'test';

export default CONFIG;
