import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { theme } from '../../../../apps/mobile/src/theme';
import Icon from 'react-native-vector-icons/AntDesign';

interface SocialLoginButtonProps {
  provider: 'google';
  onPress: () => void;
  loading?: boolean;
  title?: string;
}

const SocialLoginButton: React.FC<SocialLoginButtonProps> = ({
  provider,
  onPress,
  loading = false,
  title,
}) => {
  const config = {
    icon: 'googleplus' as const,
    text: title || 'Google ile Giriş',
    colors: ['#DB4437', '#DC4C3E'],
    iconColor: '#FFFFFF',
  };

  return (
    <TouchableOpacity
      style={[styles.container, { opacity: loading ? 0.7 : 1 }]}
      onPress={onPress}
      disabled={loading}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={config.colors as [string, string]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.content}>
          <Icon
            name={config.icon}
            size={20}
            color={config.iconColor}
            style={styles.icon}
          />
          <Text style={styles.text}>{config.text}</Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '80%',
    height: 48,
    borderRadius: theme.borderRadius.md,
    overflow: 'hidden',
    alignSelf: 'center',
    ...theme.shadows.md,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    marginRight: theme.spacing.sm,
  },
  text: {
    color: '#FFFFFF',
    fontSize: theme.typography.sm,
    fontWeight: theme.typography.semibold,
  },
});

export default SocialLoginButton; 