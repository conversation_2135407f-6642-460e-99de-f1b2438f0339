import React, { useState } from 'react';
import { View, Image, Text, StyleSheet } from 'react-native';

interface DefaultAvatarProps {
  size?: number;
  style?: any;
  name?: string;
}

const DefaultAvatar: React.FC<DefaultAvatarProps> = ({
  size = 40,
  style,
  name = '',
}) => {
  const getInitials = (fullName: string) => {
    if (!fullName) return '?';
    const names = fullName.trim().split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    }
    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  };

  const getBackgroundColor = (name: string) => {
    if (!name) return '#E5E7EB';
    
    const colors = [
      '#6366F1', // Indigo
      '#8B5CF6', // Violet  
      '#06B6D4', // Cyan
      '#10B981', // Emerald
      '#F59E0B', // Amber
      '#EF4444', // Red
      '#EC4899', // Pink
      '#3B82F6', // Blue
    ];
    
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    const index = Math.abs(hash) % colors.length;
    return colors[index];
  };

  return (
    <View 
      style={[
        defaultAvatarStyles.container,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: getBackgroundColor(name),
        },
        style
      ]}
    >
      <Text style={[defaultAvatarStyles.initials, { fontSize: size * 0.35 }]}>
        {getInitials(name)}
      </Text>
    </View>
  );
};

interface AvatarProps {
  uri?: string | null;
  size?: number;
  style?: any;
  name?: string;
}

const Avatar: React.FC<AvatarProps> = ({
  uri,
  size = 40,
  style,
  name = '',
}) => {
  const [imageError, setImageError] = useState(false);

  // URI kontrolü - null, undefined, boş string veya hata durumunda DefaultAvatar göster
  const shouldShowDefault = !uri ||
                           uri === null ||
                           uri === undefined ||
                           uri.trim() === '' ||
                           imageError;

  if (shouldShowDefault) {
    return (
      <DefaultAvatar
        size={size}
        name={name}
        style={style}
      />
    );
  }

  return (
    <Image
      source={{ uri }}
      style={[
        styles.image,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
        },
        style
      ]}
      onError={() => setImageError(true)}
      onLoadStart={() => setImageError(false)}
    />
  );
};

const styles = StyleSheet.create({
  image: {
    backgroundColor: '#f0f0f0',
  },
});

const defaultAvatarStyles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  initials: {
    color: '#FFFFFF',
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default Avatar;