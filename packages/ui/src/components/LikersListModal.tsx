import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  Image,
} from 'react-native';
import Icon from './Icon';
import { theme } from '../../../../apps/mobile/src/theme';
import { apiService } from '../../../../apps/mobile/src/services/api';

interface LikersListModalProps {
  visible: boolean;
  postId: string;
  onClose: () => void;
  onUserPress?: (userId: string) => void;
}

interface Liker {
  id: string;
  username: string;
  full_name?: string;
  avatar_url?: string;
  liked_at?: string;
  is_verified?: boolean;
  is_pro?: boolean;
}

const LikersListModal: React.FC<LikersListModalProps> = ({
  visible,
  postId,
  onClose,
  onUserPress
}) => {
  const [likers, setLikers] = useState<Liker[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (visible && postId) {
      loadLikers();
    }
  }, [visible, postId]);

  const loadLikers = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.getPostLikers(parseInt(postId), 1, 50);
      setLikers(response.items || []);
    } catch (err) {
      console.error('Error loading likers:', err);
      setError('Beğenenler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleUserPress = (userId: string) => {
    onUserPress?.(userId);
    onClose();
  };

  const renderLiker = ({ item }: { item: Liker }) => (
    <TouchableOpacity
      style={styles.likerItem}
      onPress={() => handleUserPress(item.id)}
      activeOpacity={0.7}
    >
      <View style={styles.avatarContainer}>
        {item.avatar_url ? (
          <Image source={{ uri: item.avatar_url }} style={styles.avatar} />
        ) : (
          <View style={styles.defaultAvatar}>
            <Icon name="user" size={20} color={theme.colors.textSecondary} />
          </View>
        )}
      </View>
      
      <View style={styles.userInfo}>
        <Text style={styles.userName}>
          {item.full_name || item.username}
        </Text>
        <Text style={styles.username}>@{item.username}</Text>
      </View>
      
      <View style={styles.likeInfo}>
        <Icon name="heart" size={16} color={theme.colors.error} />
        <Text style={styles.likeTime}>
          {item.liked_at ? formatTimeAgo(item.liked_at) : ''}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const formatTimeAgo = (dateString: string): string => {
    const now = new Date();
    const liked = new Date(dateString);
    const diffMs = now.getTime() - liked.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) {
      return `${diffMins} dakika önce`;
    } else if (diffHours < 24) {
      return `${diffHours} saat önce`;
    } else {
      return `${diffDays} gün önce`;
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Beğenenler</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Icon name="x" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          {/* Content */}
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={styles.loadingText}>Beğenenler yükleniyor...</Text>
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <Icon name="alert-circle" size={48} color={theme.colors.error} />
              <Text style={styles.errorText}>{error}</Text>
              <TouchableOpacity onPress={loadLikers} style={styles.retryButton}>
                <Text style={styles.retryText}>Tekrar Dene</Text>
              </TouchableOpacity>
            </View>
          ) : likers.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Icon name="heart" size={48} color={theme.colors.textSecondary} />
              <Text style={styles.emptyText}>Henüz beğenen yok</Text>
            </View>
          ) : (
            <FlatList
              data={likers}
              renderItem={renderLiker}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.listContainer}
            />
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: theme.typography.lg,
    fontWeight: theme.typography.bold,
    color: theme.colors.text,
  },
  closeButton: {
    padding: 4,
  },
  loadingContainer: {
    padding: theme.spacing.xl,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
  },
  errorContainer: {
    padding: theme.spacing.xl,
    alignItems: 'center',
  },
  errorText: {
    marginTop: theme.spacing.md,
    fontSize: theme.typography.base,
    color: theme.colors.error,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.lg,
  },
  retryText: {
    color: theme.colors.primary,
    fontSize: theme.typography.sm,
    fontWeight: theme.typography.medium,
  },
  emptyContainer: {
    padding: theme.spacing.xl,
    alignItems: 'center',
  },
  emptyText: {
    marginTop: theme.spacing.md,
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
  },
  listContainer: {
    paddingBottom: theme.spacing.lg,
  },
  likerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
  },
  avatarContainer: {
    marginRight: theme.spacing.md,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  defaultAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.surfaceVariant,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.medium,
    color: theme.colors.text,
  },
  username: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  likeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  likeTime: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
  },
});

export default LikersListModal; 