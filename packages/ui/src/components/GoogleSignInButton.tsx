import React, { useState } from 'react';
import { GoogleSigninButton } from '@react-native-google-signin/google-signin';
import googleSignInService from '../../../../apps/mobile/src/services/googleSignInService';

interface GoogleSignInButtonProps {
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({
  onSuccess,
  onError,
}) => {
  const [loading, setLoading] = useState(false);

  const handleSignIn = async () => {
    if (loading) return;
    
    setLoading(true);
    const result = await googleSignInService.signIn();
    setLoading(false);

    if (result.success) {
      onSuccess?.(result.data);
    } else {
      onError?.(result.error || 'Sign in failed');
    }
  };

  return (
    <GoogleSigninButton
      size={GoogleSigninButton.Size.Wide}
      color={GoogleSigninButton.Color.Dark}
      onPress={handleSignIn}
      disabled={loading}
    />
  );
};

export default GoogleSignInButton; 