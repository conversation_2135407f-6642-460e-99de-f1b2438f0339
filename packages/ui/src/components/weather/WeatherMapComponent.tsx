import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Platform,
} from 'react-native';
import Mapbox from '@rnmapbox/maps';
import Geolocation from '@react-native-community/geolocation';
import { request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import Icon from '../Icon';
import { theme } from '../../theme';

// Mapbox token setup
Mapbox.setAccessToken('pk.eyJ1IjoiZmlzaGl2byIsImEiOiJjbWJsaWphZWwwbjdpMmtxeTMwaGU5Zm4yIn0.LUiv6j3SGgFjTAJfpuuwDA');

interface WeatherMapComponentProps {
  onLocationSelect?: (coordinates: [number, number]) => void;
  initialCoordinates?: [number, number];
  style?: any;
}

const WeatherMapComponent: React.FC<WeatherMapComponentProps> = ({
  onLocationSelect,
  initialCoordinates = [29.0158, 41.0053],
  style,
}) => {
  const mapRef = useRef<Mapbox.MapView>(null);
  const [currentCoordinates, setCurrentCoordinates] = useState<[number, number]>(initialCoordinates);
  const [isLocationLoading, setIsLocationLoading] = useState(true);
  const [mapMounted, setMapMounted] = useState(false);
  const [locationPermissionGranted, setLocationPermissionGranted] = useState(false);
  const [currentZoom, setCurrentZoom] = useState(10);
  const [crosshairCoordinates, setCrosshairCoordinates] = useState<[number, number]>(initialCoordinates);

  useEffect(() => {
    let isMounted = true;

    const initializeLocation = async () => {
      try {
        const permission = Platform.OS === 'ios'
          ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
          : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

        const result = await request(permission);
        if (!isMounted) return;

        if (result !== RESULTS.GRANTED) {
          setDefaultLocation();
          return;
        }
        setLocationPermissionGranted(true);

        Geolocation.getCurrentPosition(
          (position) => {
            if (!isMounted) return;

            const userLocation: [number, number] = [
              Number(position.coords.longitude),
              Number(position.coords.latitude)
            ];

            setCurrentCoordinates(userLocation);
            setCrosshairCoordinates(userLocation);
            setIsLocationLoading(false);
          },
          (error) => {
            if (isMounted) {
              setDefaultLocation();
            }
          },
          {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 10000
          }
        );
      } catch (error) {
        if (isMounted) {
          setDefaultLocation();
        }
      }
    };

    initializeLocation();

    return () => {
      isMounted = false;
    };
  }, []);

  const setDefaultLocation = () => {
    const defaultLocation: [number, number] = [29.0100, 41.0082]; // İstanbul
    setCurrentCoordinates(defaultLocation);
    setCrosshairCoordinates(defaultLocation);
    setLocationPermissionGranted(true);
    setIsLocationLoading(false);
  };

  const goToUserLocation = () => {
    Geolocation.getCurrentPosition(
      (position) => {
        const userLocation: [number, number] = [
          Number(position.coords.longitude),
          Number(position.coords.latitude)
        ];

        setCurrentCoordinates([userLocation[0] + 0.0001, userLocation[1] + 0.0001]);

        setTimeout(() => {
          setCurrentCoordinates(userLocation);
        }, 50);
      },
      (error) => {
        console.log('Location error:', error);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000
      }
    );
  };

  const handleRegionDidChange = React.useCallback(async () => {
    if (!mapMounted || !mapRef.current) return;

    try {
      const centerPromise = mapRef.current.getCenter();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout')), 1000)
      );

      const center = await Promise.race([centerPromise, timeoutPromise]);

      if (center && Array.isArray(center) && center.length >= 2) {
        const [longitude, latitude] = center;
        if (typeof longitude === 'number' && typeof latitude === 'number' &&
            !isNaN(longitude) && !isNaN(latitude) &&
            longitude >= -180 && longitude <= 180 &&
            latitude >= -90 && latitude <= 90) {
          setCrosshairCoordinates([Number(longitude), Number(latitude)]);

          if (onLocationSelect) {
            onLocationSelect([Number(longitude), Number(latitude)]);
          }
        }
      }
    } catch (error) {
      // Sessizce devam et
    }
  }, [mapMounted, onLocationSelect]);

  if (isLocationLoading) {
    return (
      <View style={[styles.loadingContainer, style]}>
        <View style={styles.loadingContent}>
          <Icon name="map" size={40} color={theme.colors.primary} />
          <Text style={styles.loadingText}>Harita yükleniyor...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <Mapbox.MapView
        ref={mapRef}
        style={styles.map}
        styleURL="mapbox://styles/mapbox/outdoors-v12"
        zoomEnabled={true}
        scrollEnabled={true}
        pitchEnabled={true}
        rotateEnabled={true}
        compassEnabled={false}
        scaleBarEnabled={false}
        logoEnabled={false}
        attributionEnabled={false}
        onDidFinishLoadingMap={() => setMapMounted(true)}
        onCameraChanged={handleRegionDidChange}
      >
        <Mapbox.Camera
          centerCoordinate={[Number(currentCoordinates[0]), Number(currentCoordinates[1])]}
          zoomLevel={currentZoom}
          animationMode="easeTo"
          animationDuration={400}
        />

        {locationPermissionGranted && (
          <Mapbox.LocationPuck
            visible={true}
            pulsing={{ isEnabled: false }}
            puckBearingEnabled={true}
            puckBearing="heading"
          />
        )}
      </Mapbox.MapView>

      {/* Center Crosshair */}
      <View style={styles.crosshair}>
        <View style={styles.crosshairHorizontal} />
        <View style={styles.crosshairVertical} />
        <View style={styles.crosshairCenter} />
      </View>

      {/* Crosshair Coordinates */}
      <View style={styles.locationInfo}>
        <Text style={styles.locationText}>
          {crosshairCoordinates && crosshairCoordinates[0] !== undefined && crosshairCoordinates[1] !== undefined
            ? `${crosshairCoordinates[1].toFixed(6)}°K, ${crosshairCoordinates[0].toFixed(6)}°D`
            : 'Koordinatlar yükleniyor...'}
        </Text>
      </View>

      {/* Geolocation Button */}
      <View style={styles.geolocateContainer}>
        <TouchableOpacity
          style={styles.geolocateButton}
          onPress={goToUserLocation}
          activeOpacity={0.7}
        >
          <Icon
            name="my-location"
            size={20}
            color={theme.colors.primary}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    overflow: 'hidden',
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
  },
  loadingText: {
    fontSize: theme.typography.lg,
    fontWeight: theme.typography.semibold,
    color: theme.colors.text,
    marginTop: theme.spacing.md,
  },
  map: {
    flex: 1,
    overflow: 'hidden',
  },
  // Sniper Crosshair
  crosshair: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 120,
    height: 120,
    marginTop: -60,
    marginLeft: -60,
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
  },
  crosshairHorizontal: {
    position: 'absolute',
    width: 120,
    height: 1,
    backgroundColor: '#FF0000',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.5,
    shadowRadius: 2,
    elevation: 5,
  },
  crosshairVertical: {
    position: 'absolute',
    width: 1,
    height: 120,
    backgroundColor: '#FF0000',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.5,
    shadowRadius: 2,
    elevation: 5,
  },
  crosshairCenter: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#FF0000',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.5,
    shadowRadius: 2,
    elevation: 5,
  },
  locationInfo: {
    position: 'absolute',
    top: 20,
    alignSelf: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignItems: 'center',
  },
  locationText: {
    fontSize: 10,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  geolocateContainer: {
    position: 'absolute',
    top: 20,
    right: theme.spacing.md,
  },
  geolocateButton: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});

export default WeatherMapComponent; 