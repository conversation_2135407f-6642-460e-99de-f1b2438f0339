{"name": "@fishivo/ui", "version": "1.0.0", "description": "Shared UI components for Fishivo", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx"}, "keywords": ["ui", "components", "react", "react-native"], "author": "Fishivo Team", "license": "MIT", "dependencies": {"@fishivo/hooks": "workspace:*", "@fishivo/services": "workspace:*", "@fishivo/shared": "workspace:*", "@fishivo/utils": "workspace:*", "lucide-react-native": "^0.523.0", "react": "^18.2.0", "react-native": "^0.74.4", "react-native-svg": "^15.12.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-native": "^0.72.0", "eslint": "^8.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": ">=18.0.0", "react-native": ">=0.70.0"}}