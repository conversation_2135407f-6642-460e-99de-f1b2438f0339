import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['src/index.ts'],
  format: ['cjs', 'esm'],
  dts: {
    resolve: true,
    compilerOptions: {
      composite: false,
      skipLibCheck: true,
    },
  },
  splitting: false,
  sourcemap: true,
  clean: true,
  external: [
    '@supabase/supabase-js',
    'axios',
    'react-native',
    'react-native-image-picker',
    '@react-native-async-storage/async-storage',
    'react-native-config'
  ],
});