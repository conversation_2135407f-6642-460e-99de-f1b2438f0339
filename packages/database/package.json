{"name": "@fishivo/database", "version": "1.0.0", "description": "Database schemas and migrations for Fishivo", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "migrate": "node dist/migrate.js", "seed": "node dist/seed.js", "lint": "eslint src/**/*.ts"}, "keywords": ["fishivo", "database", "migrations", "schemas"], "author": "Fishivo Team", "license": "MIT", "dependencies": {"@fishivo/shared": "workspace:*", "@supabase/supabase-js": "^2.39.3"}, "devDependencies": {"@types/node": "^20.5.9", "eslint": "^8.49.0", "typescript": "^5.2.2"}, "files": ["dist"]}